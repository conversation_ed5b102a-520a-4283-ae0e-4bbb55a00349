<h1 align="center">
  MHC Voice Recommendator Backend
</h1>

<h4 align="center">Backend service for a voice-based AI recommendation system</h4>
<br>

# Prerequisites

- To develop:
  - [Node.js](https://nodejs.org/) (v18 or higher)
  - [Google Cloud SDK](https://cloud.google.com/sdk/docs/install)
  - [Visual Studio Code](https://code.visualstudio.com/)
  - [Yarn](https://classic.yarnpkg.com/lang/en/docs/install/)
  - [Prettier Formatter for VS Code](https://marketplace.visualstudio.com/items?itemName=esbenp.prettier-vscode). Formatting rules at `.prettierrc.cjs`

<br>

# How To Use for Development

To clone and run this application, make sure you have all the [prerequisites](#prerequisites) installed on your computer.

From your command line:

```bash
# Clone this repository
$ git clone [repository-url]

# Go into the repository
$ cd recomendador-voz-backend

# Install dependencies
$ yarn install

# Setup .env file

# Run the server in development mode
$ yarn dev
```

<br>

# Build and Deploy

To deploy this application to Google Cloud Run, from your command line:

```bash
# Build and deploy to Google Cloud Run
$ yarn deploy
```

<br>

# System Architecture

The system consists of the following main components:

- **WebSocket Server**: Handles real-time communication with clients using Socket.IO
- **Speech-to-Text Service**: Uses Google's Speech-to-Text API to convert audio input to text
- **AI**: Uses Google's Vertex AI with Gemini 2.0 Flash model to process text queries
- **Text-to-Speech Service**: Uses Speech Tools Server to convert AI responses to speech
